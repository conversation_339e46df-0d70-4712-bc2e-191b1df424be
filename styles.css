/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-red: #D32F2F;
    --dark-gray: #212121;
    --light-gray: #B0BEC5;
    --white: #FFFFFF;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
    --max-width: 1200px;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif, 'Arial Unicode MS', 'Tahoma';
    line-height: 1.6;
    color: var(--white);
    background-color: var(--white);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    direction: rtl;
    text-align: right;
}

/* Animation */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Header Styles - Optimisé */
.header {
    background: var(--white);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-top {
    background: var(--primary-red);
    color: var(--white);
    padding: 1rem 0;
}

.header-content {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 1rem;
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 1rem;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.625rem;
}
.logo img {
    width: 150px; /* Ajustez la valeur selon vos besoins */
    height: auto; /* Pour conserver les proportions */
}

.header-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 0.75rem;
}

.language-switch {
    display: flex;
    gap: 0.375rem;
}

.lang-btn {
    background: transparent;
    border: 2px solid var(--white);
    color: var(--white);
    padding: 0.375rem 0.75rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
    min-height: 2.5rem;
    font-weight: 500;
}

.lang-btn.active, .lang-btn:hover {
    background: var(--white);
    color: var(--primary-red);
}

.search-container {
    position: relative;
}

.search-bar {
    padding: 0.5rem 2.25rem 0.5rem 0.75rem;
    border: 2px solid var(--light-gray);
    border-radius: var(--border-radius);
    width: 12.5rem;
    transition: var(--transition);
    font-size: 0.9rem;
    min-height: 2.5rem;
}

.search-bar:focus {
    outline: none;
    border-color: var(--white);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

.search-btn {
    position: absolute;
    right: 0.25rem;
    top: 50%;
    transform: translateY(-50%);
    background: var(--white);
    color: var(--primary-red);
    border: none;
    padding: 0.375rem 0.625rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    min-height: 2rem;
}

.search-btn:hover {
    background: #f5f5f5;
}

.search-btn .material-icons {
    font-size: 1.25rem;
}

/* Navigation Styles - Optimisé */
.nav {
    background: var(--white);
    border-bottom: 1px solid var(--light-gray);
}

.nav-container {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 0;
    transition: transform 0.3s ease, opacity 0.3s ease, visibility 0.3s ease;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: block;
    padding: 0.75rem 1.25rem;
    text-decoration: none;
    color: var(--dark-gray);
    font-weight: 500;
    transition: var(--transition);
    border-bottom: 3px solid transparent;
    font-size: 1rem;
}

.nav-link:hover, .nav-link.active {
    color: var(--primary-red);
    border-bottom-color: var(--primary-red);
}

.nav-link:focus {
    outline: 2px solid var(--primary-red);
    outline-offset: 2px;
    background: rgba(211, 47, 47, 0.1);
}

/* Mobile Menu */
.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    font-size: 1.75rem;
    color: var(--white);
    cursor: pointer;
    padding: 0.5rem;
    z-index: 10000;
    position: absolute;
    top: 1rem;
    right: 1rem;
}

.mobile-menu-btn:focus {
    outline: 2px solid var(--primary-red);
    outline-offset: 2px;
}

/* Categories Bar - Corrigé et optimisé */
.categories-bar {
    background: var(--white);
    padding: 0.75rem 0;
    border-bottom: 1px solid #CFD8DC;
}

.categories-container {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 1rem;
}

.section-title {
    text-align: center;
    font-size: 2rem;
    color: var(--primary-red);
    margin-bottom: 2.5rem;
    font-weight: 700;
}

.category-tabs {
    display: flex;
    gap: 0.5rem;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    justify-content: center;
    flex-wrap: wrap;
}

.category-tabs::-webkit-scrollbar {
    display: none;
}

.category-tab {
    background: var(--white);
    border: 2px solid var(--primary-red);
    color: var(--primary-red);
    padding: 0.625rem 1.25rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    font-size: 1rem;
    min-height: 2.75rem;
    white-space: nowrap;
    flex-shrink: 0;
}

.category-tab:hover, .category-tab.active {
    background: var(--primary-red);
    color: var(--white);
}

/* Hero Banner */
.hero {
    background: linear-gradient(135deg, var(--primary-red) 0%, #EF5350 100%);
    color: var(--white);
    padding: 7.5rem 0;
    text-align: center;
    position: relative;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.1)" points="0,1000 1000,0 1000,1000"/></svg>');
    background-size: cover;
}

.hero-content {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 1rem;
    position: relative;
    z-index: 2;
}

.hero h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

/* Product Sections */
.products-section {
    padding: 3rem 0;
}

.container {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 1rem;
}

.category-section {
    margin-bottom: 3rem;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.category-section.hidden {
    display: none;
    opacity: 0;
}

.category-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    gap: 0.75rem;
}

.category-header h3 {
    font-size: 1.75rem;
    color: var(--primary-red);
    font-weight: 600;
}

.category-header .material-icons {
    font-size: 1.5rem;
    color: var(--primary-red);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(15.625rem, 1fr));
    gap: 1.5rem;
}

.product-card {
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.product-card:hover {
    transform: translateY(-0.375rem);
    box-shadow: var(--shadow-lg);
}

.product-image {
    width: 100%;
    height: 11.25rem;
    background: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--dark-gray);
    font-size: 1rem;
}

.product-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.product-info {
    padding: 1rem;
}

.product-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-gray);
    margin-bottom: 0.75rem;
}

.view-details-btn {
    background: var(--primary-red);
    color: var(--white);
    border: none;
    padding: 0.625rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
    width: 100%;
    font-size: 1rem;
    min-height: 3rem;
}

.view-details-btn:hover {
    background: #B71C1C;
}

/* Product Modal - Updated for scrollability and full content visibility */
.product-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.product-modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 2rem;
    max-width: 90%;
    width: 37.5rem;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    transform: scale(0.8);
    transition: transform 0.3s ease;
    color: var(--dark-gray);
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.product-modal.active .modal-content {
    transform: scale(1);
}

.modal-close-btn {
    background: var(--primary-red);
    color: var(--white);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 1rem;
    min-height: 2.75rem;
}

.modal-close-btn:not(.secondary) {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.25rem 0.5rem;
    font-size: 1.5rem;
    line-height: 1;
}

.modal-close-btn:hover {
    background: #B71C1C;
}

.modal-title {
    font-size: 1.5rem;
    color: var(--primary-red);
    margin-bottom: 1rem;
}

.modal-product-image {
    max-width: 100%;
    height: auto;
    max-height: 50vh; /* Limit image height to ensure description visibility */
    object-fit: contain;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.modal-description {
    font-size: 1rem;
    line-height: 1.5;
    color: var(--dark-gray);
    margin-bottom: 1rem;
}

/* Ensure body doesn't scroll when modal is open */
body.modal-open {
    overflow: hidden;
}

/* About Section */
.about {
    padding: 5rem 0;
    background-color: #F5F5F5;
}

.about .container {
    text-align: center;
}

.about-title {
    font-size: 2.5rem;
    color: var(--primary-red);
    margin-bottom: 1.875rem;
    font-weight: 700;
}

.about-description {
    font-size: 1.1rem;
    color: var(--dark-gray);
    max-width: 50rem;
    margin: 0 auto 3.125rem;
    line-height: 1.6;
}

.about-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(15.625rem, 1fr));
    gap: 1.25rem;
    margin-bottom: 3.125rem;
}

.feature {
    padding: 1.25rem;
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.feature:hover {
    transform: translateY(-0.625rem);
}

.feature h3 {
    font-size: 1.3rem;
    color: var(--primary-red);
    margin-bottom: 0.9375rem;
}

.feature p {
    font-size: 1rem;
    color: var(--dark-gray);
    line-height: 1.5;
}

/* Footer */
.footer {
    background: var(--dark-gray);
    color: var(--white);
    padding: 2.5rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(12.5rem, 1fr));
    gap: 2rem;
    margin-bottom: 1.5rem;
}

.footer-section h4 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: var(--primary-red);
}

.footer-section p, .footer-section a {
    color: #CFD8DC;
    text-decoration: none;
    line-height: 1.7;
    font-size: 0.9rem;
}

.footer-section a:hover {
    color: var(--primary-red);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.contact-details {
    margin-bottom: 1rem;
}

.contact-item {
    margin-bottom: 0.5rem;
}

.social-icons {
    display: flex;
    gap: 0.75rem;
}

.social-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background: var(--primary-red);
    color: var(--white);
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition);
}

.social-icon:hover {
    background: #B71C1C;
    transform: translateY(-0.125rem);
}

.footer-bottom {
    border-top: 1px solid #78909C;
    padding-top: 1rem;
    text-align: center;
    color: #CFD8DC;
    font-size: 0.85rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 0.75rem;
    }

    .header-right {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
    }

    .search-bar {
        width: 100%;
        max-width: 18.75rem;
    }

    .mobile-menu-btn {
        display: block;
    }

    .nav-menu {
        display: block;
        visibility: hidden;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.95);
        flex-direction: column;
        transform: translateX(-100%);
        opacity: 0;
        z-index: 9999;
        padding: 4rem 1rem;
        justify-content: flex-start;
        align-items: center;
        overflow-y: auto;
        transition: transform 0.3s ease, opacity 0.3s ease, visibility 0.3s ease;
    }

    .nav-menu.active {
        visibility: visible;
        transform: translateX(0);
        opacity: 1;
    }

    .nav-link {
        padding: 1.25rem 2rem;
        font-size: 1.3rem;
        text-align: center;
    }

    .category-tabs {
        justify-content: flex-start;
        flex-wrap: nowrap;
    }

    .hero h2 {
        font-size: 1.8rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .hero {
        padding: 5rem 0;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .category-tab {
        font-size: 0.9rem;
        padding: 0.5rem 0.75rem;
        min-height: 2.5rem;
    }

    .modal-content {
        width: 95%;
        max-width: 95%;
        padding: 1.5rem;
    }

    .modal-product-image {
        max-height: 40vh;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(18.75rem, 1fr));
    }
}

@media (min-width: 1025px) {
    .category-tabs {
        justify-content: center;
    }
}

/* RTL (Right-to-Left) Support for Arabic */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] .header-content {
    grid-template-columns: 1fr auto;
}

[dir="rtl"] .header-right {
    justify-content: flex-start;
}

[dir="rtl"] .nav-menu {
    direction: rtl;
}

[dir="rtl"] .search-container {
    direction: ltr;
}

[dir="rtl"] .search-bar {
    text-align: right;
    padding: 0.5rem 0.75rem 0.5rem 2.25rem;
}

[dir="rtl"] .search-btn {
    left: 0.5rem;
    right: auto;
}

[dir="rtl"] .products-grid {
    direction: rtl;
}

[dir="rtl"] .product-card {
    text-align: right;
}

[dir="rtl"] .modal-content {
    text-align: right;
}

[dir="rtl"] .about-features {
    direction: rtl;
}

[dir="rtl"] .feature {
    text-align: right;
}

/* Arabic font improvements */
[lang="ar"] {
    font-family: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', sans-serif;
    font-size: 1.1em;
    line-height: 1.8;
}

[lang="ar"] h1, [lang="ar"] h2, [lang="ar"] h3 {
    font-weight: bold;
    line-height: 1.4;
}

[lang="ar"] .nav-link {
    font-weight: 500;
}

[lang="ar"] .lang-btn {
    font-weight: 600;
}