-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Hôte : 127.0.0.1:3306
-- <PERSON><PERSON><PERSON><PERSON> le : sam. 12 juil. 2025 à 13:44
-- Version du serveur : 9.1.0
-- Version de PHP : 8.3.14

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de données : `madjour_industries`
--

-- --------------------------------------------------------

--
-- Structure de la table `admins`
--

DROP TABLE IF EXISTS `admins`;
CREATE TABLE IF NOT EXISTS `admins` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `admins`
--

INSERT INTO `admins` (`id`, `username`, `password`, `created_at`) VALUES
(1, 'admin', 'admin123', '2025-07-08 10:18:31');

-- --------------------------------------------------------

--
-- Structure de la table `categories`
--

DROP TABLE IF EXISTS `categories`;
CREATE TABLE IF NOT EXISTS `categories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name_en` varchar(255) NOT NULL,
  `name_fr` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_name_en` (`name_en`),
  KEY `idx_name_fr` (`name_fr`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `categories`
--

INSERT INTO `categories` (`id`, `name_en`, `name_fr`) VALUES
(1, 'Lighters & Gas', 'Briquets & Gaz'),
(2, 'Toothbrushes', 'Brosses à Dents'),
(3, 'Adhesive Glue', 'Colle Adhésive'),
(4, 'Baby Products', 'Produits Bébé'),
(5, 'Insecticides', 'Insecticides');

-- --------------------------------------------------------

--
-- Structure de la table `products`
--

DROP TABLE IF EXISTS `products`;
CREATE TABLE IF NOT EXISTS `products` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `category_id` int NOT NULL,
  `description_en` text NOT NULL,
  `description_fr` text NOT NULL,
  `image_path` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `products`
--

INSERT INTO `products` (`id`, `name`, `category_id`, `description_en`, `description_fr`, `image_path`) VALUES
(8, 'baby product', 4, 'baby product', 'produit des bébé', 'Uploads/1752326095__D3A3917.webp'),
(9, 'baby product', 4, 'baby product', 'produit de bébé', 'Uploads/1752326203_04.webp'),
(10, 'baby product', 4, 'baby product', 'produit des bébé', 'Uploads/1752326234_125_ml.webp'),
(11, 'baby product', 4, 'baby product', 'produit des bébé', 'Uploads/1752326295_IMG_0157.webp'),
(12, 'baby product', 4, 'baby product', 'produit des bébé', 'Uploads/1752326347_IMG_0158.webp'),
(13, 'Lighters', 1, 'Lighters', 'Briquets', 'Uploads/1752326465_17_mini.webp'),
(14, 'Lighters', 1, 'Lighters', 'Briquets', 'Uploads/1752326495_f05.webp'),
(15, 'Lighters', 1, 'Lighters', 'Briquets', 'Uploads/1752326520_fewf.webp'),
(16, 'Lighters', 1, 'Lighters', 'Briquets', 'Uploads/1752326550_hm_05_G.webp'),
(17, 'Lighters', 1, 'Lighters', 'Briquets', 'Uploads/1752326575_IMG_0358.webp'),
(18, 'Lighters', 1, 'Lighters', 'Briquets', 'Uploads/1752326607_m70.webp');

--
-- Contraintes pour les tables déchargées
--

--
-- Contraintes pour la table `products`
--
ALTER TABLE `products`
  ADD CONSTRAINT `fk_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE RESTRICT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
