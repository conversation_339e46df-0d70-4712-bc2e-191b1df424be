<?php
// translations.php
require_once 'db_connect.php';

// Basic file-based caching
$cache_file = 'cache/translations.json';
$cache_ttl = 3600; // 1 hour

$translations = [];
if (file_exists($cache_file) && (time() - filemtime($cache_file)) < $cache_ttl) {
    $translations = json_decode(file_get_contents($cache_file), true);
} else {
    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        $products = $conn->query("SELECT id, name, description_en, description_fr FROM products")->fetchAll();
        $categories = $conn->query("SELECT name_en, name_fr FROM categories")->fetchAll();

        $translations = [
    'en' => [
        'title' => 'Trusted Products',
        'home' => 'Home',
        'categories' => 'Categories',
        'products' => 'Products',
        'about' => 'About Us',
        'contact' => 'Contact',
        'admin' => 'Admin',
        'welcome' => 'Trusted Products for Everyday Life',
        'tagline' => 'Madjour Industries Quality You Can Count On',
        'view_details' => 'View Details',
        'category' => 'Category',
        'all_categories' => 'All Categories',
        'categories.lighters_&_gas' => 'Lighters & Gas',
        'categories.toothbrushes' => 'Toothbrushes',
        'categories.adhesive_glue' => 'Adhesive Glue',
        'categories.baby_products' => 'Baby Products',
        'categories.insecticides' => 'Insecticides'
    ],
    'fr' => [
        'title' => 'Produits de Confiance',
        'home' => 'Accueil',
        'categories' => 'Catégories',
        'products' => 'Nos Produits',
        'about' => 'À Propos',
        'contact' => 'Contact',
        'admin' => 'Admin',
        'welcome' => 'Produits de Confiance pour la Vie Quotidienne',
        'tagline' => 'Qualité Madjour Industries sur laquelle vous pouvez compter',
        'view_details' => 'Voir Détails',
        'category' => 'Catégorie',
        'all_categories' => 'Toutes les Catégories',
        'categories.lighters_&_gas' => 'Briquets & Gaz',
        'categories.toothbrushes' => 'Brosses à Dents',
        'categories.adhesive_glue' => 'Colle Adhésive',
        'categories.baby_products' => 'Produits Bébé',
        'categories.insecticides' => 'Insecticides'
    ]
];
        foreach ($products as $product) {
            $product_id = strtolower(str_replace(' ', '_', $product['name']));
            $translations['en']['products.' . $product_id] = $product['name'];
            $translations['fr']['products.' . $product_id] = $product['name'];
            $translations['en']['products.' . $product_id . '_desc'] = $product['description_en'];
            $translations['fr']['products.' . $product_id . '_desc'] = $product['description_fr'];
        }

        foreach ($categories as $category) {
            $category_slug = strtolower(str_replace(' ', '_', $category['name_en']));
            $translations['en']['categories.' . $category_slug] = $category['name_en'];
            $translations['fr']['categories.' . $category_slug] = $category['name_fr'];
        }

        // Save to cache
        if (!is_dir('cache')) {
            mkdir('cache', 0755, true);
        }
        file_put_contents($cache_file, json_encode($translations));
    } catch (Exception $e) {
        file_put_contents('debug.log', "translations.php: {$e->getMessage()}\n", FILE_APPEND);
        $translations = []; // Fallback to empty translations
    }
}

// Set content type to JavaScript
header('Content-Type: text/javascript');

// Output JavaScript
echo "const translations = " . json_encode($translations) . ";";
?>