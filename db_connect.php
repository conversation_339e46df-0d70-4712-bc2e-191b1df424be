<?php
// db_connect.php
session_start();

class Database {
    private static $instance = null;
    private $conn;
    private $host = 'localhost';
    private $port = 3306; // Change to 8080 only if MySQL is explicitly configured to use it
    private $dbname = 'madjour_industries';
    private $username = 'root'; // Replace with dedicated user
    private $password = ''; // Replace with secure password
    private $maxRetries = 3;
    private $retryDelay = 1; // Reduced delay for faster retries (in seconds)

    private function __construct() {
        $this->connect();
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new Database();
        }
        return self::$instance;
    }

    private function connect() {
        $dsn = "mysql:host={$this->host};port={$this->port};dbname={$this->dbname};charset=utf8mb4";
        $attempt = 1;

        while ($attempt <= $this->maxRetries) {
            try {
                $this->conn = new PDO($dsn, $this->username, $this->password, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4",
                    PDO::ATTR_TIMEOUT => 5 // Set 5-second timeout for connection
                ]);
                file_put_contents('debug.log', "Database connected successfully on attempt $attempt at " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
                return;
            } catch (PDOException $e) {
                $error = "Connection attempt $attempt failed on port {$this->port}: {$e->getMessage()} (Code: {$e->getCode()}) at " . date('Y-m-d H:i:s') . "\n";
                file_put_contents('debug.log', $error, FILE_APPEND);
                if ($attempt === $this->maxRetries) {
                    throw new Exception("Failed to connect to database after $attempt attempts: {$e->getMessage()}");
                }
                sleep($this->retryDelay);
                $attempt++;
            }
        }
    }

    public function getConnection() {
        if ($this->conn === null) {
            $this->connect();
        }
        return $this->conn;
    }

    // Optional: Method to close connection explicitly (useful for testing)
    public function closeConnection() {
        $this->conn = null;
    }
}

// Optional: Load credentials from environment variables for better security
// Uncomment and configure if using environment variables
/*
$dotenv = parse_ini_file('.env');
if ($dotenv) {
    $this->host = $dotenv['DB_HOST'] ?? 'localhost';
    $this->port = $dotenv['DB_PORT'] ?? 3306;
    $this->dbname = $dotenv['DB_NAME'] ?? 'madjour_industries';
    $this->username = $dotenv['DB_USER'] ?? 'root';
    $this->password = $dotenv['DB_PASS'] ?? '';
}
*/
?>