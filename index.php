<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="description" content="Madjour Industries - Quality lighters, gas, toothbrushes, adhesives, baby products, and insecticides.">
    <meta name="keywords" content="Madjour Industries, lighters, gas, toothbrushes, adhesive glue, baby products, insecticides">
    <meta name="robots" content="index, follow">
    <title>Madjour Industries</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" integrity="sha512-Fo3rlrZj/k7ujTnHg4CGR2D7kSs0v4LLanw2qksYuRlEzO+tcaEPQogQ0KaoGN26/zrn20ImR1DfuLWnOo7aBA==" crossorigin="anonymous" referrerpolicy="no-referrer">
    <link rel="stylesheet" href="styles.css?v=1.0">
</head>
<body>
    <?php
    require_once 'db_connect.php';
    $db = Database::getInstance()->getConnection();
    $categories = $db->query("SELECT id, name_en, name_ar FROM categories ORDER BY id")->fetchAll();
    $products = $db->query("
        SELECT p.id, p.name, p.category_id, p.description_en, p.description_ar, p.image_path, c.name_en AS category_name_en
        FROM products p
        JOIN categories c ON p.category_id = c.id
        ORDER BY p.category_id, p.id
    ")->fetchAll();
    $products_by_category = [];
    foreach ($products as $product) {
        $products_by_category[$product['category_id']][] = $product;
    }
    ?>
    <header class="header">
        <div class="header-top">
            <div class="header-content">
                <div class="logo">
                    <img src="img/logo/logo madjour.png" alt="MADJOUR Logo" width="60" height="60" loading="lazy">
                </div>
                <div class="header-right">
                    <div class="language-switch">
                        <button class="lang-btn active" data-lang="en">EN</button>
                        <button class="lang-btn" data-lang="ar">العربية</button>
                    </div>
                    <div class="search-container">
                        <input type="text" class="search-bar" placeholder="Search products..." data-i18n-placeholder="search.placeholder">
                        <button class="search-btn"><span class="material-icons">search</span></button>
                    </div>
                </div>
            </div>
        </div>
        <nav class="nav">
            <div class="nav-container">
                <button class="mobile-menu-btn" aria-label="Toggle Menu">☰</button>
                <ul class="nav-menu">
                    <li class="nav-item"><a href="#home" class="nav-link active" data-i18n="nav.home">Home</a></li>
                    <li class="nav-item"><a href="#categories" class="nav-link" data-i18n="nav.categories">Categories</a></li>
                    <li class="nav-item"><a href="#products" class="nav-link" data-i18n="nav.products">Products</a></li>
                    <li class="nav-item"><a href="#about" class="nav-link" data-i18n="nav.about">About Us</a></li>
                    <li class="nav-item"><a href="#contact" class="nav-link" data-i18n="nav.contact">Contact</a></li>
                </ul>
            </div>
        </nav>
    </header>

    <section class="hero" id="home">
        <div class="hero-content">
            <h2 data-i18n="hero.title">Trusted Products for Everyday Life</h2>
            <p data-i18n="hero.subtitle">Madjour Industries Quality You Can Count On</p>
        </div>
    </section>

    <section class="categories-bar" id="categories">
        <div class="categories-container">
            <h2 class="section-title" data-i18n="categories.title">Categories</h2>
            <div class="category-tabs" id="category-tabs">
                <?php foreach ($categories as $index => $category): ?>
                    <button class="category-tab <?php echo $index === 0 ? 'active' : ''; ?>" data-category="category-<?php echo $category['id']; ?>" data-i18n="categories.<?php echo strtolower(str_replace(' & ', '_', $category['name_en'])); ?>">
                        <?php echo htmlspecialchars($category['name_en']); ?>
                    </button>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <section class="products-section" id="products">
        <div class="container">
            <h2 class="section-title" data-i18n="products.title">Our Products</h2>
            <?php foreach ($categories as $index => $category): ?>
                <div class="category-section <?php echo $index !== 0 ? 'hidden' : ''; ?>" id="category-<?php echo $category['id']; ?>-section">
                    <div class="category-header">
                        <h3 data-i18n="categories.<?php echo strtolower(str_replace(' & ', '_', $category['name_en'])); ?>">
                            <?php echo htmlspecialchars($category['name_en']); ?>
                        </h3>
                    </div>
                    <div class="products-grid">
                        <?php if (isset($products_by_category[$category['id']])): ?>
                            <?php foreach ($products_by_category[$category['id']] as $product): ?>
                                <div class="product-card" data-product-id="<?php echo $product['id']; ?>">
                                    <div class="product-image">
                                        <img src="<?php echo htmlspecialchars($product['image_path']); ?>" alt="<?php echo htmlspecialchars($product['name']); ?>" width="200" height="200" loading="lazy">
                                    </div>
                                    <div class="product-info">
                                        <h4 class="product-name"><?php echo htmlspecialchars($product['name']); ?></h4>
                                        <button class="view-details-btn" data-i18n="products.view_details" data-product-id="<?php echo $product['id']; ?>" data-description-en="<?php echo htmlspecialchars($product['description_en']); ?>" data-description-ar="<?php echo htmlspecialchars($product['description_ar']); ?>">View Details</button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p>No products available in this category.</p>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </section>

    <section class="about" id="about">
        <div class="container">
            <h1 class="about-title">About EURL MADJOUR HICHEM</h1>
            <p class="about-description" data-i18n="about.description">EURL MADJOUR HICHEM is a leading company in multiple sectors, comprising several manufacturing units, including lighters, gas, glue, insecticides, and semi-pharmaceutical materials.</p>
            <div class="about-features">
                <div class="feature">
                    <h3>Lighters Manufacturing</h3>
                    <p>High-quality lighter production with strict quality control</p>
                </div>
                <div class="feature">
                    <h3>Gas Production</h3>
                    <p>Safe and efficient gasonzeppa gas manufacturing solutions</p>
                </div>
                <div class="feature">
                    <h3>Industrial Materials</h3>
                    <p>Production of glue, insecticides, and semi-pharmaceuticals</p>
                </div>
            </div>
        </div>
    </section>

    <footer class="footer" id="contact">
        <div class="container footer-content">
            <div class="footer-section">
                <h4 data-i18n="footer.quick_links">Quick Links</h4>
                <ul class="footer-links">
                    <li><a href="#home" data-i18n="nav.home">Home</a></li>
                    <li><a href="#categories" data-i18n="nav.categories">Categories</a></li>
                    <li><a href="#products" data-i18n="nav.products">Products</a></li>
                    <li><a href="#about" data-i18n="nav.about">About Us</a></li>
                    <li><a href="#contact" data-i18n="nav.contact">Contact</a></li>
                </ul>
            </div>
            <div class="footer-section contact-info">
                <h4 data-i18n="footer.contact_title">Contact Info</h4>
                <div class="contact-details">
                    <div class="contact-item">
                        <p>(+213)770787280</p>
                    </div>
                    <div class="contact-item">
                        <p>Loktarat groupe 42 SECTION 21 AIN AZEL SÉTIF</p>
                    </div>
                </div>
                <div class="social-icons">
                    <a href="https://www.facebook.com/EURLMadjourHich" class="social-icon" target="_blank" rel="noopener noreferrer"><i class="fab fa-facebook-f"></i></a>
                    <a href="https://www.tiktok.com/@eurl.madjour.hich" class="social-icon" target="_blank" rel="noopener noreferrer"><i class="fab fa-tiktok"></i></a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>© 2025 Madjour Industries. <span data-i18n="footer.rights">All rights reserved.</span></p>
        </div>
    </footer>

    <div class="product-modal" id="product-modal">
        <div class="modal-content">
            <button class="modal-close-btn" aria-label="Close Modal">×</button>
            <h3 class="modal-title"></h3>
            <div class="modal-image"><img class="modal-product-image" src="" alt=""></div>
            <p class="modal-description"></p>
            <button class="modal-close-btn secondary" data-i18n="modal.close">Close</button>
        </div>
    </div>

    <script src="script.js?v=1.0"></script>
</body>
</html>