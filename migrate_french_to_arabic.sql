-- Migration script to replace French with Arabic in the database
-- Run this script to update existing database structure and content

-- First, add the new Arabic columns
ALTER TABLE `categories` ADD COLUMN `name_ar` varchar(255) NOT NULL DEFAULT '';
ALTER TABLE `products` ADD COLUMN `description_ar` text NOT NULL DEFAULT '';

-- Update categories with Arabic translations
UPDATE `categories` SET `name_ar` = 'ولاعات وغاز' WHERE `id` = 1;
UPDATE `categories` SET `name_ar` = 'فرش الأسنان' WHERE `id` = 2;
UPDATE `categories` SET `name_ar` = 'غراء لاصق' WHERE `id` = 3;
UPDATE `categories` SET `name_ar` = 'منتجات الأطفال' WHERE `id` = 4;
UPDATE `categories` SET `name_ar` = 'مبيدات حشرية' WHERE `id` = 5;

-- Update products with Arabic descriptions
UPDATE `products` SET `description_ar` = 'منتج للأطفال' WHERE `category_id` = 4;
UPDATE `products` SET `description_ar` = 'ولاعات' WHERE `category_id` = 1;

-- Add index for the new Arabic column
ALTER TABLE `categories` ADD KEY `idx_name_ar` (`name_ar`);

-- Drop the old French columns
ALTER TABLE `categories` DROP COLUMN `name_fr`;
ALTER TABLE `categories` DROP KEY `idx_name_fr`;
ALTER TABLE `products` DROP COLUMN `description_fr`;

-- Verify the changes
SELECT 'Categories updated:' as status;
SELECT id, name_en, name_ar FROM categories;

SELECT 'Products updated:' as status;
SELECT id, name, category_id, description_en, description_ar FROM products LIMIT 5;
