-- Migration script to replace French with Arabic in the database
-- This script updates existing database structure and data

-- Step 1: Add new Arabic columns
ALTER TABLE `categories` ADD COLUMN `name_ar` varchar(255) NOT NULL DEFAULT '';
ALTER TABLE `products` ADD COLUMN `description_ar` text NOT NULL DEFAULT '';

-- Step 2: Update categories with Arabic translations
UPDATE `categories` SET `name_ar` = 'ولاعات وغاز' WHERE `id` = 1;
UPDATE `categories` SET `name_ar` = 'فرش الأسنان' WHERE `id` = 2;
UPDATE `categories` SET `name_ar` = 'غراء لاصق' WHERE `id` = 3;
UPDATE `categories` SET `name_ar` = 'منتجات الأطفال' WHERE `id` = 4;
UPDATE `categories` SET `name_ar` = 'مبيدات حشرية' WHERE `id` = 5;

-- Step 3: Update products with Arabic translations
UPDATE `products` SET `description_ar` = 'منتج للأطفال' WHERE `category_id` = 4;
UPDATE `products` SET `description_ar` = 'ولاعات' WHERE `category_id` = 1;

-- Step 4: Drop French columns and rename Arabic columns
ALTER TABLE `categories` DROP COLUMN `name_fr`;
ALTER TABLE `categories` DROP INDEX `idx_name_fr`;
ALTER TABLE `categories` ADD INDEX `idx_name_ar` (`name_ar`);

ALTER TABLE `products` DROP COLUMN `description_fr`;

-- Step 5: Rename Arabic columns to replace French naming convention
-- Note: MySQL doesn't support direct column rename in all versions, so we use CHANGE
ALTER TABLE `categories` CHANGE `name_ar` `name_ar` varchar(255) NOT NULL;
ALTER TABLE `products` CHANGE `description_ar` `description_ar` text NOT NULL;
