// admin.js
document.addEventListener('DOMContentLoaded', () => {
    const addProductForm = document.querySelector('.add-product-form');
    const editProductForm = document.querySelector('#edit-product-form');
    const deleteProductForms = document.querySelectorAll('.delete-product-form');
    const addCategoryForm = document.querySelector('.add-category-form');
    const editCategoryForm = document.querySelector('#edit-category-form');
    const deleteCategoryForms = document.querySelectorAll('.delete-category-form');
    const editProductButtons = document.querySelectorAll('.edit-product-btn');
    const editCategoryButtons = document.querySelectorAll('.edit-category-btn');
    const cancelEditButtons = document.querySelectorAll('.cancel-edit-btn');

    // Function to show messages
    function showMessage(message, type) {
        const messageEl = document.createElement('div');
        messageEl.textContent = message;
        messageEl.style.cssText = `
            position: fixed;
            top: 16px;
            right: 16px;
            background: ${type === 'error' ? '#ef4444' : '#10b981'};
            color: white;
            padding: 10px 16px;
            border-radius: 6px;
            z-index: 1000;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            font-size: 0.9rem;
        `;
        document.body.appendChild(messageEl);
        setTimeout(() => messageEl.style.transform = 'translateX(0)', 100);
        setTimeout(() => {
            messageEl.style.transform = 'translateX(100%)';
            setTimeout(() => messageEl.remove(), 300);
        }, 3000);
    }

    // Handle form submissions
    const handleFormSubmit = async (form, isMultipart = false) => {
        const formData = isMultipart ? new FormData(form) : new FormData(form);
        try {
            const response = await fetch('admin.php', {
                method: 'POST',
                body: formData
            });
            const text = await response.text();
            const parser = new DOMParser();
            const doc = parser.parseFromString(text, 'text/html');

            // Extract message
            const message = doc.querySelector('.message')?.textContent || doc.querySelector('.error')?.textContent;
            if (message) {
                showMessage(message, message.includes('Error') ? 'error' : 'success');
            }

            // Update product table
            if (form.classList.contains('add-product-form') || form.classList.contains('edit-product-form') || form.classList.contains('delete-product-form')) {
                const productTable = doc.querySelector('.data-table:nth-of-type(1) tbody');
                document.querySelector('.data-table:nth-of-type(1) tbody').innerHTML = productTable.innerHTML;
                const select = doc.querySelector('#category_id').innerHTML;
                document.querySelector('#category_id').innerHTML = select;
                document.querySelector('#edit_category_id').innerHTML = select;
            }

            // Update category table
            if (form.classList.contains('add-category-form') || form.classList.contains('edit-category-form') || form.classList.contains('delete-category-form')) {
                const categoryTable = doc.querySelector('.data-table:nth-of-type(2) tbody');
                document.querySelector('.data-table:nth-of-type(2) tbody').innerHTML = categoryTable.innerHTML;
                const select = doc.querySelector('#category_id').innerHTML;
                document.querySelector('#category_id').innerHTML = select;
                document.querySelector('#edit_category_id').innerHTML = select;
            }

            form.reset();
            editProductForm.style.display = 'none';
            editCategoryForm.style.display = 'none';

            // Re-attach event listeners for new delete buttons
            reattachDeleteListeners();
        } catch (error) {
            console.error('Error:', error);
            showMessage('An error occurred. Please try again.', 'error');
        }
    };

    // Attach form submission handlers
    if (addProductForm) {
        addProductForm.addEventListener('submit', (e) => {
            e.preventDefault();
            handleFormSubmit(addProductForm, true);
        });
    }
    if (editProductForm) {
        editProductForm.addEventListener('submit', (e) => {
            e.preventDefault();
            handleFormSubmit(editProductForm, true);
        });
    }
    if (addCategoryForm) {
        addCategoryForm.addEventListener('submit', (e) => {
            e.preventDefault();
            handleFormSubmit(addCategoryForm);
        });
    }
    if (editCategoryForm) {
        editCategoryForm.addEventListener('submit', (e) => {
            e.preventDefault();
            handleFormSubmit(editCategoryForm);
        });
    }

    // Handle delete form submissions
    function reattachDeleteListeners() {
        const newDeleteProductForms = document.querySelectorAll('.delete-product-form');
        const newDeleteCategoryForms = document.querySelectorAll('.delete-category-form');
        
        newDeleteProductForms.forEach(form => {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                if (confirm('Are you sure you want to delete this product?')) {
                    handleFormSubmit(form);
                }
            });
        });

        newDeleteCategoryForms.forEach(form => {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                if (confirm('Are you sure you want to delete this category? It must have no associated products.')) {
                    handleFormSubmit(form);
                }
            });
        });
    }

    reattachDeleteListeners();

    // Handle edit product buttons
    editProductButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            document.querySelector('#edit_product_id').value = btn.dataset.id;
            document.querySelector('#edit_product_name').value = btn.dataset.name;
            document.querySelector('#edit_category_id').value = btn.dataset.categoryId;
            document.querySelector('#edit_description_en').value = btn.dataset.descEn;
            document.querySelector('#edit_description_fr').value = btn.dataset.descFr;
            document.querySelector('#edit_existing_image_path').value = btn.dataset.imagePath;
            editProductForm.style.display = 'block';
            editCategoryForm.style.display = 'none';
        });
    });

    // Handle edit category buttons
    editCategoryButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            document.querySelector('#edit_category_id').value = btn.dataset.id;
            document.querySelector('#edit_category_name_en').value = btn.dataset.nameEn;
            document.querySelector('#edit_category_name_fr').value = btn.dataset.nameFr;
            editCategoryForm.style.display = 'block';
            editProductForm.style.display = 'none';
        });
    });

    // Handle cancel buttons
    cancelEditButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            editProductForm.style.display = 'none';
            editCategoryForm.style.display = 'none';
        });
    });
});